from PyPDF2 import PdfReader
import docx
import re
import io

def parse_resume(file_name, file_bytes):
    """
    Parse resume from file bytes and extract structured information
    """
    text = ""

    try:
        if file_name.lower().endswith(".pdf"):
            # Create a BytesIO object from bytes for PDF reading
            pdf_file = io.BytesIO(file_bytes)
            pdf = PdfReader(pdf_file)
            for page in pdf.pages:
                text += page.extract_text() + "\n"

        elif file_name.lower().endswith(".docx"):
            # Create a BytesIO object from bytes for DOCX reading
            docx_file = io.BytesIO(file_bytes)
            doc = docx.Document(docx_file)
            for para in doc.paragraphs:
                text += para.text + "\n"

        else:
            # Try to decode as text file
            text = file_bytes.decode('utf-8', errors='ignore')

    except Exception as e:
        print(f"Error parsing file {file_name}: {e}")
        # Fallback: try to decode as text
        try:
            text = file_bytes.decode('utf-8', errors='ignore')
        except:
            text = "Error: Could not parse file content"

    return extract_fields(text)

def extract_fields(text):
    """
    Extract structured information from resume text
    """
    data = {}

    # Extract email
    email_match = re.search(r'[\w\.-]+@[\w\.-]+\.[\w]+', text)
    if email_match:
        data["email"] = email_match.group(0)

    # Extract name (first line that looks like a name)
    lines = text.split('\n')
    for line in lines[:5]:  # Check first 5 lines
        line = line.strip()
        if line and len(line.split()) >= 2 and len(line) < 50:
            # Simple heuristic: 2+ words, not too long, no special chars
            if not re.search(r'[\d@#$%^&*()_+=\[\]{}|;:,.<>?/~`]', line):
                data["name"] = line
                break

    # Extract phone number
    phone_patterns = [
        r'\+?\d{1,3}[\s-]?\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}',
        r'\(?\d{3}\)?[\s-]?\d{3}[\s-]?\d{4}',
        r'\+\d{1,3}\s\d{10}'
    ]
    for pattern in phone_patterns:
        phone_match = re.search(pattern, text)
        if phone_match:
            data["phone"] = phone_match.group(0)
            break

    # Extract LinkedIn URL
    linkedin_match = re.search(r'linkedin\.com/in/[\w-]+', text, re.IGNORECASE)
    if linkedin_match:
        data["linkedin"] = "https://" + linkedin_match.group(0)

    # Extract GitHub URL
    github_match = re.search(r'github\.com/[\w-]+', text, re.IGNORECASE)
    if github_match:
        data["github"] = "https://" + github_match.group(0)

    # Extract technical skills (enhanced detection)
    tech_skills = []
    skill_keywords = [
        'python', 'javascript', 'java', 'c++', 'c#', 'react', 'angular', 'vue',
        'node.js', 'express', 'django', 'flask', 'fastapi', 'spring', 'laravel',
        'html', 'css', 'sql', 'mongodb', 'postgresql', 'mysql', 'redis',
        'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'git', 'jenkins',
        'tensorflow', 'pytorch', 'scikit-learn', 'pandas', 'numpy',
        'langchain', 'openai', 'machine learning', 'deep learning', 'ai'
    ]

    text_lower = text.lower()
    for skill in skill_keywords:
        if skill in text_lower:
            tech_skills.append(skill.title())

    # Remove duplicates and limit to reasonable number
    data["Technical Skills"] = list(set(tech_skills))[:15]

    # Extract education (simple detection)
    education_keywords = ['university', 'college', 'institute', 'school', 'bachelor', 'master', 'phd', 'degree']
    education_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in education_keywords):
            education_lines.append(line.strip())

    if education_lines:
        data["education"] = education_lines[:3]  # Limit to 3 entries

    # Extract work experience (simple detection)
    experience_keywords = ['experience', 'work', 'employment', 'position', 'role', 'job']
    experience_lines = []
    for line in lines:
        if any(keyword in line.lower() for keyword in experience_keywords) and len(line.strip()) > 10:
            experience_lines.append(line.strip())

    if experience_lines:
        data["experience"] = experience_lines[:5]  # Limit to 5 entries

    # Add parsing metadata
    data["parsing_notes"] = []
    if not data.get("email"):
        data["parsing_notes"].append("Email not found in resume")
    if not data.get("name"):
        data["parsing_notes"].append("Name not clearly identified")
    if not data.get("Technical Skills"):
        data["parsing_notes"].append("No technical skills detected")

    return data
