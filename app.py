import streamlit as st
import requests
import uuid
import json

# Page configuration
st.set_page_config(
    page_title="Resume Intelligence Assistant",
    page_icon="📄",
    layout="wide"
)

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'resume_data' not in st.session_state:
    st.session_state.resume_data = None

# API Configuration
API_BASE_URL = "http://localhost:8000"

# Helper functions
def make_api_request(method, endpoint, **kwargs):
    """Make API request without authentication"""
    try:
        if 'timeout' not in kwargs:
            kwargs['timeout'] = 30

        if method.upper() == 'POST':
            response = requests.post(f"{API_BASE_URL}{endpoint}", **kwargs)
        elif method.upper() == 'GET':
            response = requests.get(f"{API_BASE_URL}{endpoint}", **kwargs)
        else:
            return None

        return response
    except Exception as e:
        st.error(f"API Error: {str(e)}")
        return None

def safe_json_response(response):
    """Safely extract JSO<PERSON> from response"""
    if response is None:
        return None
    try:
        return response.json()
    except:
        return None

# Initialize session state
if 'session_id' not in st.session_state:
    st.session_state.session_id = None
if 'resume_data' not in st.session_state:
    st.session_state.resume_data = None

# Main app without authentication
st.title("🤖 Resume Intelligence Assistant")
st.markdown("*Your AI-powered career companion - No login required!*")

# Sidebar for file upload
with st.sidebar:
    st.header("📄 Upload Resume")
    uploaded_file = st.file_uploader(
        "Choose your resume file",
        type=['pdf', 'docx'],
        help="Upload your resume in PDF or DOCX format"
    )

    if st.button("Process Resume") and uploaded_file:
        with st.spinner("Processing your resume..."):
            try:
                # Prepare file for upload
                files = {"file": (uploaded_file.name, uploaded_file.getvalue(), uploaded_file.type)}

                # Try to call the API first
                response = make_api_request("POST", "/upload_resume_simple", files=files)

                if response and hasattr(response, 'status_code') and response.status_code == 200:
                    # API call successful
                    result = safe_json_response(response)
                    st.write(f"**Debug - API Response:** {result}")  # Debug info

                    if result and result.get("session_id"):
                        st.session_state.session_id = result["session_id"]
                        st.session_state.resume_data = result.get("parsed", {})

                        st.success("Resume processed successfully!")
                        st.info(f"Session ID: {result['session_id'][:8]}...")
                        st.write(f"**Debug - Stored resume data:** {st.session_state.resume_data}")  # Debug info

                        # Show extracted information
                        if result.get("parsed"):
                            with st.expander("📄 Extracted Information"):
                                parsed_data = result["parsed"]
                                if parsed_data.get("name"):
                                    st.write(f"**Name:** {parsed_data['name']}")
                                if parsed_data.get("email"):
                                    st.write(f"**Email:** {parsed_data['email']}")
                                if parsed_data.get("phone"):
                                    st.write(f"**Phone:** {parsed_data['phone']}")
                                if parsed_data.get("Technical Skills"):
                                    st.write(f"**Skills:** {', '.join(parsed_data['Technical Skills'])}")

                        st.rerun()
                    else:
                        st.error("Invalid response from server")
                else:
                    # API failed, create demo session
                    st.warning("⚠️ API not available, running in demo mode")
                    st.session_state.session_id = str(uuid.uuid4())
                    st.session_state.resume_data = {
                        "name": "Demo User",
                        "email": "<EMAIL>",
                        "phone": "******-0123",
                        "Technical Skills": ["Python", "JavaScript", "React", "Node.js", "AWS"],
                        "experience": ["Software Engineer at TechCorp", "Junior Developer at StartupXYZ"],
                        "education": ["BS Computer Science - University of Technology"]
                    }
                    st.success("Resume processed in demo mode!")
                    st.info(f"Demo Session ID: {st.session_state.session_id[:8]}...")
                    st.write(f"**Debug - Demo resume data:** {st.session_state.resume_data}")  # Debug info
                    st.rerun()

            except Exception as e:
                st.error(f"Error processing resume: {str(e)}")
                # Fallback to demo mode
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.resume_data = {"name": "Demo User", "Technical Skills": ["Python", "JavaScript"]}
                st.info("Running in demo mode due to error")
                st.rerun()

    # Show current session info
    if st.session_state.session_id:
        st.success(f"✅ Active Session")
        st.write(f"ID: {st.session_state.session_id[:8]}...")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")

    st.markdown("---")
    st.subheader("👤 Session Info")
    st.write("**Status:** No login required")
    st.write("**Mode:** Resume processing enabled")

# Main content area
if not st.session_state.session_id:
    st.info("👈 Please upload your resume from the sidebar to get started.")

    # Show demo information
    col1, col2, col3 = st.columns(3)

    with col1:
        st.subheader("📄 Resume Analysis")
        st.write("Upload your resume and get detailed parsing with extracted skills, experience, and contact information.")

    with col2:
        st.subheader("🛠 Improvement Suggestions")
        st.write("Get AI-powered suggestions to enhance your resume and make it more attractive to employers.")

    with col3:
        st.subheader("💼 Job Recommendations")
        st.write("Find relevant job openings based on your skills and experience using advanced search algorithms.")

else:
    # Action buttons interface for active session
    st.header(f"🤖 Resume Intelligence Assistant")
    st.subheader("What would you like me to help you with?")

    # Action buttons in a grid
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🔍 Find Job Recommendations", use_container_width=True):
            with st.spinner("Finding job recommendations..."):
                # Debug information
                st.write("**Debug Info:**")
                st.write(f"Session ID: {st.session_state.session_id}")
                st.write(f"Resume data available: {st.session_state.resume_data is not None}")

                # Get job recommendations based on resume data
                if st.session_state.resume_data and st.session_state.resume_data.get('Technical Skills'):
                    tech_skills = st.session_state.resume_data['Technical Skills']
                    primary_skill = tech_skills[0] if tech_skills else "Software"
                    st.write(f"Using skills from resume: {tech_skills}")
                else:
                    primary_skill = "Python"  # Default
                    tech_skills = [primary_skill]
                    st.warning("No resume data found. Using default skills for demo.")

                    # Offer to load demo data
                    if st.button("🔄 Load Demo Resume Data"):
                        st.session_state.resume_data = {
                            "name": "Demo User",
                            "Technical Skills": ["Python", "JavaScript", "React"],
                            "experience": [{"title": "Software Engineer", "company": "Tech Corp"}]
                        }
                        st.success("Demo resume data loaded!")
                        st.rerun()

                # Generate job recommendations
                from agents.job import generate_mock_jobs
                demo_jobs = generate_mock_jobs(tech_skills, "mid")

                st.subheader("💼 Job Recommendations")
                st.info(f"Found {len(demo_jobs)} relevant job opportunities for {primary_skill} developers")

                for i, job in enumerate(demo_jobs, 1):
                    with st.container():
                        # Job header with title and company
                        col1, col2, col3 = st.columns([2, 1, 1])

                        with col1:
                            st.markdown(f"**{i}. {job['title']}**")
                            st.write(f"🏢 {job['company']} | 📍 {job['source']}")
                            st.write(f"� {job['location']} | 💰 {job['salary']}")

                        with col2:
                            # Relevance score
                            score = job['relevance_score']
                            color = "green" if score >= 80 else "orange" if score >= 60 else "red"
                            st.markdown(f"<div style='text-align: center; color: {color}; font-weight: bold;'>Match: {score}%</div>", unsafe_allow_html=True)

                        with col3:
                            # Apply button with direct link
                            apply_url = job.get('apply_url', job.get('url', '#'))
                            st.markdown(f"""
                                <a href="{apply_url}" target="_blank" style="text-decoration: none;">
                                    <button style="
                                        background-color: #4CAF50;
                                        color: white;
                                        padding: 10px 20px;
                                        border: none;
                                        border-radius: 5px;
                                        cursor: pointer;
                                        width: 100%;
                                        font-weight: bold;
                                    ">🚀 Apply Now</button>
                                </a>
                            """, unsafe_allow_html=True)

                        # Job description
                        st.write(f"📝 {job['content']}")

                        # Apply links section
                        col_apply1, col_apply2 = st.columns(2)
                        with col_apply1:
                            apply_url = job.get('apply_url', job.get('url', '#'))
                            st.markdown(f"[🎯 **Apply Directly**]({apply_url})")
                        with col_apply2:
                            st.markdown(f"[🔗 View Job Details]({job.get('url', '#')})")

                        st.markdown("---")

        if st.button("📊 Show Resume Data", use_container_width=True):
            with st.spinner("Loading resume data..."):
                st.subheader("📄 Your Resume Data")

                # Debug information
                st.write("**Debug Info:**")
                st.write(f"Session ID exists: {st.session_state.session_id is not None}")
                st.write(f"Resume data exists: {st.session_state.resume_data is not None}")
                if st.session_state.resume_data:
                    st.write(f"Resume data keys: {list(st.session_state.resume_data.keys())}")

                if st.session_state.resume_data:
                    st.json(st.session_state.resume_data)
                else:
                    st.warning("No resume data available. Please upload and process a resume first.")

                    # Try to provide fallback demo data
                    if st.button("🔄 Load Demo Data"):
                        st.session_state.resume_data = {
                            "name": "Demo User",
                            "email": "<EMAIL>",
                            "Technical Skills": ["Python", "JavaScript", "React", "Node.js"],
                            "experience": [
                                {"title": "Software Engineer", "company": "Tech Corp", "duration": "2 years"}
                            ],
                            "education": [
                                {"degree": "Computer Science", "school": "University"}
                            ]
                        }
                        st.success("Demo data loaded!")
                        st.rerun()

    with col2:
        if st.button("📈 Improve My Resume", use_container_width=True):
            with st.spinner("Analyzing your resume..."):
                st.subheader("🛠 Improvement Suggestions")
                improvements = [
                    "Add more quantifiable achievements with specific metrics",
                    "Include relevant certifications (AWS, Google Cloud)",
                    "Expand the technical skills section with frameworks",
                    "Add a professional summary at the top",
                    "Include links to GitHub and portfolio projects"
                ]
                for improvement in improvements:
                    st.write(f"• {improvement}")

        if st.button("❓ Get Help", use_container_width=True):
            with st.spinner("Loading help information..."):
                st.info("🚀 Welcome to Resume Intelligence Assistant! I can help you with:\n\n🔍 **Job Recommendations** - Find relevant positions based on your skills\n\n📈 **Resume Improvements** - Get suggestions to enhance your resume\n\n📊 **Resume Analysis** - View parsed data from your uploaded resume\n\nSimply click any of the action buttons to get started!")

    # Show session statistics
    st.markdown("---")
    st.subheader("📊 Session Information")
    col1, col2 = st.columns(2)
    with col1:
        st.write(f"**Mode:** No Authentication Required")
        st.write(f"**Session:** {st.session_state.session_id[:8]}...")
    with col2:
        st.write(f"**Status:** Resume Uploaded ✅")
        if st.session_state.resume_data and st.session_state.resume_data.get('name'):
            st.write(f"**User:** {st.session_state.resume_data['name']}")
        else:
            st.write(f"**Data:** Processing enabled")

# Footer
st.markdown("---")
st.markdown("*Resume Intelligence Assistant - Powered by AI (Demo Mode - No Authentication Required)*")
