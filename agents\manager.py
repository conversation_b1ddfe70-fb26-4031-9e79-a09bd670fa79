import os
from langchain_google_genai import ChatGoogleGenerativeAI

# Load environment variables from .env file
def load_env():
    try:
        with open('.env', 'r') as f:
            for line in f:
                if '=' in line and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value.strip('"')
    except FileNotFoundError:
        pass
    except Exception:
        pass

# Load environment variables
load_env()
from langgraph.graph import StateGraph, END
from agents.parser import parser_node
from agents.improver import improver_node
from agents.job import job_node
from utils.db import session_manager
from prompts.supervisor_prompt import PROMPT as MANAGER_PROMPT
from typing import TypedDict, Optional, Dict, Any
import json

class ResumeStateType(TypedDict, total=False):
    file_name: str
    file_bytes: bytes
    history: dict
    session_id: str
    user_name: str
    email: str
    parsed: dict
    improvement: list
    jobs: list

def create_resume_state(file_name: str = None, file_bytes: bytes = None, session_id: str = None) -> ResumeStateType:
    return {
        "file_name": file_name,
        "file_bytes": file_bytes,
        "history": {},
        "session_id": session_id,
        "user_name": None,
        "email": None,
        "parsed": {},
        "improvement": [],
        "jobs": []
    }

# Initialize Google AI model with error handling
try:
    model = ChatGoogleGenerativeAI(
        model="gemini-pro",
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        temperature=0.4
    )
except Exception as e:
    print(f"Warning: Google AI model initialization failed: {e}")
    model = None

class ManagerAgent:
    def __init__(self):
        self.model = model
        self.session_manager = session_manager

    def process_user_request(self, user_input: str, session_id: str = None, user_name: str = None) -> Dict[str, Any]:
        """
        Main entry point for user interactions. Manager agent decides what to do.
        """
        try:
            # Get or create session
            if session_id:
                session = self.session_manager.get_session(session_id)
                if not session:
                    return {"error": "Session not found. Please start a new session."}
            elif user_name:
                session = self.session_manager.get_session_by_name(user_name)
                if not session:
                    return {"error": f"No session found for user '{user_name}'. Please upload a resume first."}
                session_id = session.get("session_id")
            else:
                return {"error": "Please provide either a session_id or user_name."}

            # Add user message to conversation history
            self.session_manager.add_to_conversation_history(session_id, {
                "role": "user",
                "message": user_input
            })

            # Get user context for decision making
            context = self.session_manager.get_user_context(session_id)

            # Analyze user request and decide action
            decision = self._analyze_user_request(user_input, context)

            response = self._execute_decision(decision, session_id, user_input)

            # Add manager response to conversation history
            self.session_manager.add_to_conversation_history(session_id, {
                "role": "manager",
                "message": response.get("message", "Task completed"),
                "action": decision["action"]
            })

            return response

        except Exception as e:
            return {"error": f"Manager agent error: {str(e)}"}

    def _analyze_user_request(self, user_input: str, context: Dict) -> Dict[str, Any]:
        """
        Analyze user request and decide which agent to call or what action to take
        """
        user_input_lower = user_input.lower()

        # Check for job-related requests
        if any(keyword in user_input_lower for keyword in ["job", "jobs", "opening", "position", "career", "hiring"]):
            if context.get("has_resume"):
                return {"action": "find_jobs", "reason": "User requested job recommendations"}
            else:
                return {"action": "request_resume", "reason": "Need resume for job search"}

        # Check for improvement requests
        if any(keyword in user_input_lower for keyword in ["improve", "suggestion", "better", "enhance", "feedback"]):
            if context.get("has_resume"):
                return {"action": "improve_resume", "reason": "User requested resume improvements"}
            else:
                return {"action": "request_resume", "reason": "Need resume for improvements"}

        # Check for parsing/analysis requests
        if any(keyword in user_input_lower for keyword in ["parse", "analyze", "extract", "read", "show"]):
            if context.get("has_resume"):
                return {"action": "show_parsed", "reason": "User wants to see parsed resume"}
            else:
                return {"action": "request_resume", "reason": "Need resume to parse"}

        # Default: provide general help
        return {"action": "general_help", "reason": "General inquiry"}

    def _execute_decision(self, decision: Dict, session_id: str, user_input: str) -> Dict[str, Any]:
        """
        Execute the decided action
        """
        action = decision["action"]
        session = self.session_manager.get_session(session_id)

        if action == "find_jobs":
            return self._handle_job_search(session_id)

        elif action == "improve_resume":
            return self._handle_resume_improvement(session_id)

        elif action == "show_parsed":
            return self._handle_show_parsed(session_id)

        elif action == "request_resume":
            return {
                "message": f"Hi {session.get('user_name', 'there')}! I need your resume to help you with that. Please upload your resume first.",
                "action_required": "upload_resume"
            }

        elif action == "general_help":
            return self._handle_general_help(session)

        else:
            return {"message": "I'm not sure how to help with that. Could you please clarify?"}

    def _handle_job_search(self, session_id: str) -> Dict[str, Any]:
        """Handle job search requests"""
        session = self.session_manager.get_session(session_id)

        if not session.get("resume_data", {}).get("parsed"):
            return {"error": "Resume not found. Please upload your resume first."}

        # Run job search workflow
        state = create_resume_state(session_id=session_id)
        state["parsed"] = session["resume_data"]["parsed"]
        state["history"] = {"email": session.get("email")}

        # Call job agent
        updated_state = job_node(state)

        # Update session with job results
        self.session_manager.update_session(session_id, {
            "job_recommendations": updated_state.get("jobs", []),
            "context": {**session.get("context", {}), "jobs_searched": True}
        })

        return {
            "message": f"Found {len(updated_state.get('jobs', []))} job recommendations for you!",
            "jobs": updated_state.get("jobs", []),
            "session_id": session_id
        }

    def _handle_resume_improvement(self, session_id: str) -> Dict[str, Any]:
        """Handle resume improvement requests"""
        session = self.session_manager.get_session(session_id)

        if not session.get("resume_data", {}).get("parsed"):
            return {"error": "Resume not found. Please upload your resume first."}

        # Run improvement workflow
        state = ResumeState(session_id=session_id)
        state["parsed"] = session["resume_data"]["parsed"]

        # Call improver agent
        updated_state = improver_node(state)

        # Update session with improvement results
        self.session_manager.update_session(session_id, {
            "improvement_suggestions": updated_state.get("improvement", [])
        })

        return {
            "message": "Here are some suggestions to improve your resume:",
            "improvements": updated_state.get("improvement", []),
            "session_id": session_id
        }

    def _handle_show_parsed(self, session_id: str) -> Dict[str, Any]:
        """Handle requests to show parsed resume"""
        session = self.session_manager.get_session(session_id)

        parsed_data = session.get("resume_data", {}).get("parsed")
        if not parsed_data:
            return {"error": "Resume not found. Please upload your resume first."}

        return {
            "message": "Here's your parsed resume data:",
            "parsed": parsed_data,
            "session_id": session_id
        }

    def _handle_general_help(self, session: Dict) -> Dict[str, Any]:
        """Handle general help requests"""
        user_name = session.get("user_name", "there")
        has_resume = bool(session.get("resume_data", {}).get("parsed"))

        if has_resume:
            message = f"Hi {user_name}! I can help you with:\n" \
                     "• Finding job recommendations\n" \
                     "• Improving your resume\n" \
                     "• Analyzing your resume data\n" \
                     "What would you like to do?"
        else:
            message = f"Hi {user_name}! I'm your Resume Intelligence Assistant. " \
                     "Please upload your resume first, and then I can help you with job recommendations, " \
                     "resume improvements, and more!"

        return {"message": message}

    def process_resume_upload(self, file_name: str, file_content: bytes, user_name: str, email: str = None) -> Dict[str, Any]:
        """
        Process new resume upload and create session
        """
        try:
            # Create new session
            session_id = self.session_manager.create_session(
                user_name=user_name,
                email=email,
                resume_data={"filename": file_name, "content": file_content}
            )

            # Run the full workflow
            state = create_resume_state(file_name=file_name, file_bytes=file_content, session_id=session_id)
            state["user_name"] = user_name
            state["email"] = email

            # Build and run the workflow
            builder = StateGraph(ResumeStateType)
            builder.add_node("parse", parser_node)
            builder.add_node("improve", improver_node)
            builder.add_node("jobs", job_node)
            builder.set_entry_point("parse")
            builder.add_edge("parse", "improve")
            builder.add_edge("improve", "jobs")
            builder.add_edge("jobs", END)
            graph = builder.compile()

            final_state = graph.invoke(state)

            # Update session with results (content is already stored during session creation)
            session = self.session_manager.get_session(session_id)
            self.session_manager.update_session(session_id, {
                "resume_data": {
                    **session.get("resume_data", {}),  # Keep existing data including content
                    "filename": file_name,
                    "parsed": final_state.get("parsed", {})
                },
                "improvement_suggestions": final_state.get("improvement", []),
                "job_recommendations": final_state.get("jobs", []),
                "email": final_state.get("email") or email,
                "context": {
                    "email_provided": bool(final_state.get("email") or email),
                    "resume_parsed": True,
                    "jobs_searched": True
                }
            })

            # Add initial conversation entry
            self.session_manager.add_to_conversation_history(session_id, {
                "role": "system",
                "message": f"Resume uploaded and processed for {user_name}"
            })

            return {
                "message": f"Welcome {user_name}! Your resume has been processed successfully.",
                "session_id": session_id,
                "parsed": final_state.get("parsed", {}),
                "improvement": final_state.get("improvement", []),
                "jobs": final_state.get("jobs", [])
            }

        except Exception as e:
            return {"error": f"Failed to process resume: {str(e)}"}

# Global manager agent instance
manager_agent = ManagerAgent()

# Legacy function for backward compatibility
def run_workflow(file_name, content):
    state = create_resume_state(file_name=file_name, file_bytes=content)
    builder = StateGraph(ResumeStateType)
    builder.add_node("parse", parser_node)
    builder.add_node("improve", improver_node)
    builder.add_node("jobs", job_node)
    builder.set_entry_point("parse")
    builder.add_edge("parse", "improve")
    builder.add_edge("improve", "jobs")
    builder.add_edge("jobs", END)
    graph = builder.compile()
    final_state = graph.invoke(state)
    return final_state