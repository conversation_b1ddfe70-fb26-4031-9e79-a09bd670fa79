from langchain.agents import Tool
from utils.file_parser import parse_resume
from prompts.parser_prompt import PROMPT

def resume_parser_tool_func(file_name, file_bytes):
    return parse_resume(file_name, file_bytes)

resume_parser_tool = Tool(
    name="ResumeParser",
    func=resume_parser_tool_func,
    description="Extract structured data from a resume"
)

def parser_node(state):
    result = resume_parser_tool_func(state["file_name"], state["file_bytes"])
    state["parsed"] = result
    if "email" in result:
        state["history"]["email"] = result["email"]
    return state